import * as React from "react"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

export interface InputProps extends React.ComponentProps<"input"> {
  showClearButton?: boolean
  onClear?: () => void
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, showClearButton, onClear, value, onChange, ...props }, ref) => {
    const hasValue = value !== undefined && value !== null && String(value).length > 0
    
    // 智能默认值：对于日期类型输入框，默认禁用清除按钮（因为原生日期控件已有清除功能）
    const defaultShowClearButton = type === 'date' ? false : true
    const effectiveShowClearButton = showClearButton !== undefined ? showClearButton : defaultShowClearButton
    
    const shouldShowClearButton = effectiveShowClearButton && hasValue && !props.disabled && !props.readOnly

    const handleClear = () => {
      if (onClear) {
        onClear()
      } else if (onChange) {
        // Create a synthetic event for the onChange handler
        const syntheticEvent = {
          target: { value: '' },
          currentTarget: { value: '' }
        } as React.ChangeEvent<HTMLInputElement>
        onChange(syntheticEvent)
      }
    }

    return (
      <div className="relative">
        <input
          type={type}
          className={cn(
            "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            shouldShowClearButton && "pr-8",
            className
          )}
          ref={ref}
          value={value}
          onChange={onChange}
          {...props}
        />
        {shouldShowClearButton && (
          <button
            type="button"
            onClick={(e) => {
              // 阻止事件冒泡，防止触发父级组件的点击事件（如模态框关闭）
              e.stopPropagation();
              e.preventDefault();
              handleClear();
            }}
            onMouseDown={(e) => {
              // 阻止 mousedown 事件冒泡
              e.stopPropagation();
              e.preventDefault();
            }}
            className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            tabIndex={-1}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Clear input</span>
          </button>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"
export { Input }
