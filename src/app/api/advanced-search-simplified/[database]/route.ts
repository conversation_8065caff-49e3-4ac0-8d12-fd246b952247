import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import { getDynamicModel } from '@/lib/dynamicModel';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/paginationUtils';
import { SearchCondition } from '@/components/AdvancedSearchRefactored';

interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Builds Prisma where clause from simplified search conditions
 * No operators needed - searchType determines the search behavior automatically
 */
function buildSimplifiedWhere(conditions: SearchCondition[], config: any) {
  if (!conditions || conditions.length === 0) {
    return {};
  }

  const whereConditions: any[] = [];

  conditions.forEach((condition) => {
    if (!condition.field || condition.value === undefined || condition.value === null) {
      return;
    }

    // Find field configuration to determine search behavior
    const fieldConfig = config.fields.find((f: any) => f.fieldName === condition.field);
    if (!fieldConfig) {
      return;
    }

    const filterType = fieldConfig.filterType;
    const searchType = fieldConfig.searchType || fieldConfig.fieldType;
    let whereClause: any = {};

    // Handle filterType-based logic first (for UI-driven behavior)
    if (filterType === 'multi_select') {
      // Multi-select always uses IN clause
      if (Array.isArray(condition.value) && condition.value.length > 0) {
        whereClause[condition.field] = {
          in: condition.value
        };
      }
    } else if (filterType === 'select') {
      // Single select uses exact match
      if (typeof condition.value === 'string' && condition.value.trim()) {
        whereClause[condition.field] = condition.value.trim();
      }
    } else if (filterType === 'date_range') {
      // Date range picker
      if (typeof condition.value === 'object' && condition.value !== null) {
        const dateRange = condition.value as { from?: string; to?: string };
        const dateConditions: any = {};

        if (dateRange.from) {
          dateConditions.gte = new Date(dateRange.from);
        }
        if (dateRange.to) {
          dateConditions.lte = new Date(dateRange.to);
        }

        if (Object.keys(dateConditions).length > 0) {
          whereClause[condition.field] = dateConditions;
        }
      }
    } else if (filterType === 'range') {
      // Number range inputs
      if (typeof condition.value === 'object' && condition.value !== null) {
        const range = condition.value as { from?: string; to?: string };
        const rangeConditions: any = {};

        if (range.from && !isNaN(Number(range.from))) {
          rangeConditions.gte = Number(range.from);
        }
        if (range.to && !isNaN(Number(range.to))) {
          rangeConditions.lte = Number(range.to);
        }

        if (Object.keys(rangeConditions).length > 0) {
          whereClause[condition.field] = rangeConditions;
        }
      }
    } else {
      // Fallback to searchType-based logic for input fields
      switch (searchType) {
        case 'exact':
          if (typeof condition.value === 'string' && condition.value.trim()) {
            whereClause[condition.field] = condition.value.trim();
          }
          break;

        case 'contains':
          if (typeof condition.value === 'string' && condition.value.trim()) {
            whereClause[condition.field] = {
              contains: condition.value.trim(),
              mode: 'insensitive'
            };
          }
          break;

        case 'starts_with':
          if (typeof condition.value === 'string' && condition.value.trim()) {
            whereClause[condition.field] = {
              startsWith: condition.value.trim(),
              mode: 'insensitive'
            };
          }
          break;

        case 'ends_with':
          if (typeof condition.value === 'string' && condition.value.trim()) {
            whereClause[condition.field] = {
              endsWith: condition.value.trim(),
              mode: 'insensitive'
            };
          }
          break;

        case 'date_range':
          if (typeof condition.value === 'object' && condition.value !== null) {
            const dateRange = condition.value as { from?: string; to?: string };
            const dateConditions: any = {};

            if (dateRange.from) {
              dateConditions.gte = new Date(dateRange.from);
            }
            if (dateRange.to) {
              dateConditions.lte = new Date(dateRange.to);
            }

            if (Object.keys(dateConditions).length > 0) {
              whereClause[condition.field] = dateConditions;
            }
          }
          break;

        case 'date':
          if (typeof condition.value === 'string' && condition.value.trim()) {
            whereClause[condition.field] = new Date(condition.value);
          }
          break;

        case 'range':
          if (typeof condition.value === 'object' && condition.value !== null) {
            const range = condition.value as { from?: string; to?: string };
            const rangeConditions: any = {};

            if (range.from && !isNaN(Number(range.from))) {
              rangeConditions.gte = Number(range.from);
            }
            if (range.to && !isNaN(Number(range.to))) {
              rangeConditions.lte = Number(range.to);
            }

            if (Object.keys(rangeConditions).length > 0) {
              whereClause[condition.field] = rangeConditions;
            }
          }
          break;

        default:
          // Handle array values or default to contains
          if (Array.isArray(condition.value) && condition.value.length > 0) {
            whereClause[condition.field] = {
              in: condition.value
            };
          } else if (typeof condition.value === 'string' && condition.value.trim()) {
            // Default to contains for text input
            whereClause[condition.field] = {
              contains: condition.value.trim(),
              mode: 'insensitive'
            };
          }
          break;
      }
    }

    if (Object.keys(whereClause).length > 0) {
      whereConditions.push(whereClause);
    }
  });

  if (whereConditions.length === 0) {
    return {};
  }

  // Combine conditions based on logic operators
  if (whereConditions.length === 1) {
    return whereConditions[0];
  }

  // For now, default to AND logic (can be enhanced later to support mixed AND/OR)
  return {
    AND: whereConditions
  };
}

export async function POST(
  request: NextRequest,
  { params }: { params: { database: string } }
) {
  try {
    const database = params.database;

    if (!database) {
      return NextResponse.json(
        { success: false, error: 'Database parameter is required' },
        { status: 400 }
      );
    }

    const body: AdvancedSearchRequest = await request.json();
    const { conditions, sortBy, sortOrder = 'desc' } = body;

    // Validate pagination parameters
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 50;
    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // Get database configuration
    const config = await getDatabaseConfig(database);
    const visibleFields = config.fields.filter((f: any) => f.isVisible).map((f: any) => f.fieldName);
    const sortableFields = config.fields.filter((f: any) => f.isSortable).map((f: any) => f.fieldName);

    // Build simplified where clause
    const where = buildSimplifiedWhere(conditions, config);

    // Build sort order
    const defaultSortField = sortableFields[0] || 'id';
    const orderBy = {
      [sortBy || defaultSortField]: sortOrder,
    };

    // Get dynamic model
    const model = getDynamicModel(database);
    if (!model) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 500 }
      );
    }

    // Build select object
    const select: Record<string, boolean> = {};
    visibleFields.forEach((f: string) => { select[f] = true; });
    select['id'] = true; // Always include ID

    // Execute query
    const data = await (model as any).findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    });
    
    const totalCount = await (model as any).count({ where });

    return NextResponse.json({
      success: true,
      data,
      pagination: buildPaginationResponse(page, limit, totalCount),
      conditions,
      config: {
        code: database,
        fields: config.fields
      }
    });

  } catch (error) {
    console.error('Simplified Advanced Search API Error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
